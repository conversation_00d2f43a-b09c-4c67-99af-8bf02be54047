{"info": {"name": "Cury Cliente API", "description": "Coleção completa da API do Cliente Cury", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "account_id", "value": "", "type": "string"}, {"key": "user_login", "value": "***********", "type": "string"}], "item": [{"name": "🔐 Autenticação", "item": [{"name": "Pré-login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userLogin\": \"{{user_login}}\",\n  \"password\": \"senha123\"\n}"}, "url": {"raw": "{{base_url}}/api/prelogin", "host": ["{{base_url}}"], "path": ["api", "prelogin"]}}}, {"name": "<PERSON><PERSON>e", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('access_token', response.data.token.access_token);", "    }", "    if (response.data && response.data.user) {", "        pm.collectionVariables.set('account_id', response.data.user.AccountId);", "        pm.collectionVariables.set('user_id', response.data.user.UserId);", "        pm.collectionVariables.set('codigo_sienge', response.data.user.CodigoSienge__c);", "    }", "}"]}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userLogin\": \"{{user_login}}\",\n  \"password\": \"{{password}}\",\n  \"deviceinfo\": \"postman-test\",\n  \"adminlogin\": \"false\",\n  \"remember\": \"false\"\n}"}, "url": {"raw": "{{base_url}}/api/login-client", "host": ["{{base_url}}"], "path": ["api", "login-client"]}}}, {"name": "Validar <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/valid-token", "host": ["{{base_url}}"], "path": ["api", "valid-token"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/where-logged", "host": ["{{base_url}}"], "path": ["api", "where-logged"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceinfo\": \"postman-test\"\n}"}, "url": {"raw": "{{base_url}}/api/refresh-data", "host": ["{{base_url}}"], "path": ["api", "refresh-data"]}}}, {"name": "Reset Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userLogin\": \"{{user_login}}\",\n  \"deviceinfo\": \"postman-test\"\n}"}, "url": {"raw": "{{base_url}}/api/resetPassword", "host": ["{{base_url}}"], "path": ["api", "resetPassword"]}}}, {"name": "Verification Code", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userLogin\": \"{{user_login}}\",\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\",\n  \"deviceinfo\": \"postman-test\"\n}"}, "url": {"raw": "{{base_url}}/api/verificationCode", "host": ["{{base_url}}"], "path": ["api", "verificationCode"]}}}, {"name": "<PERSON>en<PERSON><PERSON> <PERSON><PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userLogin\": \"{{user_login}}\"\n}"}, "url": {"raw": "{{base_url}}/api/resendWelcomeMail", "host": ["{{base_url}}"], "path": ["api", "resendWelcomeMail"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceinfo\": \"postman-test\"\n}"}, "url": {"raw": "{{base_url}}/api/logout", "host": ["{{base_url}}"], "path": ["api", "logout"]}}}]}, {"name": "Usuários", "item": [{"name": "Listar Usuários", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/showuser?AccountId={{account_id}}", "host": ["{{base_url}}"], "path": ["api", "showuser"], "query": [{"key": "AccountId", "value": "{{account_id}}"}]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/showAvatar?UserId={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "showAvatar"], "query": [{"key": "UserId", "value": "{{user_id}}"}]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userid\": \"{{user_id}}\",\n  \"dataSales\": {\n    \"Name\": \"<PERSON>\",\n    \"Email__c\": \"<EMAIL>\",\n    \"TelefoneCelular__c\": \"(11) 99999-9999\"\n  },\n  \"data\": {\n    \"alternative_name\": \"<PERSON>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/updateuser", "host": ["{{base_url}}"], "path": ["api", "updateuser"]}}}]}, {"name": "Financeiro", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"CodigoSienge__c\": \"{{codigo_sienge}}\",\n  \"UnidadeAtivoName\": \"Torre A - Apto 101\"\n}"}, "url": {"raw": "{{base_url}}/api/installments", "host": ["{{base_url}}"], "path": ["api", "installments"]}}}, {"name": "Verificar Boleto Ato", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"CodigoSienge__c\": \"{{codigo_sienge}}\",\n  \"UnidadeAtivoName\": \"Torre A - Apto 101\"\n}"}, "url": {"raw": "{{base_url}}/api/checkBoletoAto", "host": ["{{base_url}}"], "path": ["api", "checkBoletoAto"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"billReceivableId\": \"BR123456\",\n  \"installmentId\": \"INST123456\",\n  \"UnidadeAtivoName\": \"Torre A - Apto 101\"\n}"}, "url": {"raw": "{{base_url}}/api/boleto", "host": ["{{base_url}}"], "path": ["api", "boleto"]}}}, {"name": "Gerar PDF Parcelas", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"codigoSiengeUser\": \"{{codigo_sienge}}\",\n  \"UnidadeAtivoName\": \"Torre A - Apto 101\"\n}"}, "url": {"raw": "{{base_url}}/api/generatePDF", "host": ["{{base_url}}"], "path": ["api", "generatePDF"]}}}, {"name": "Nego<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"CodigoSienge__c\": \"{{codigo_sienge}}\",\n  \"UnidadeAtivoName\": \"Torre A - Apto 101\",\n  \"parcelas\": [\"INST123\", \"INST124\"],\n  \"tipoNegociacao\": \"desconto\"\n}"}, "url": {"raw": "{{base_url}}/api/negotiate", "host": ["{{base_url}}"], "path": ["api", "negotiate"]}}}]}, {"name": "Mensagens", "item": [{"name": "Contar Mensagens Não Lidas", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/message/getCountMessagesUnread", "host": ["{{base_url}}"], "path": ["api", "message", "getCountMessagesUnread"]}}}, {"name": "Listar Mensagens", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/message/showMessages?page=1&limit=20", "host": ["{{base_url}}"], "path": ["api", "message", "showMessages"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/message/MSG123456", "host": ["{{base_url}}"], "path": ["api", "message", "MSG123456"]}}}, {"name": "Marcar como Lida", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"messageId\": \"MSG123456\",\n  \"read\": true\n}"}, "url": {"raw": "{{base_url}}/api/message/toggleRead", "host": ["{{base_url}}"], "path": ["api", "message", "toggleRead"]}}}]}, {"name": "Cases", "item": [{"name": "Listar Cases", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/case", "host": ["{{base_url}}"], "path": ["api", "case"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"AccountId\": \"{{account_id}}\",\n  \"Assunto__c\": \"Dúvida sobre obra\",\n  \"Description\": \"Gostaria de saber sobre o andamento da obra\",\n  \"Classificacao__c\": \"Dúvida\"\n}"}, "url": {"raw": "{{base_url}}/api/case", "host": ["{{base_url}}"], "path": ["api", "case"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/case/CASE123456", "host": ["{{base_url}}"], "path": ["api", "case", "CASE123456"]}}}, {"name": "Cases por Con<PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/case/showCases/{{account_id}}/all", "host": ["{{base_url}}"], "path": ["api", "case", "showCases", "{{account_id}}", "all"]}}}, {"name": "Verificar Case Hoje", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/case/existsCaseToday/{{account_id}}/Dúvida", "host": ["{{base_url}}"], "path": ["api", "case", "existsCaseToday", "{{account_id}}", "Dúvida"]}}}, {"name": "Deletar Case", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"CaseId\": \"CASE123456\"\n}"}, "url": {"raw": "{{base_url}}/api/case", "host": ["{{base_url}}"], "path": ["api", "case"]}}}]}, {"name": "Propostas", "item": [{"name": "Obter Proposta", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ProposalId\": \"PROP123456\"\n}"}, "url": {"raw": "{{base_url}}/api/proposal", "host": ["{{base_url}}"], "path": ["api", "proposal"]}}}, {"name": "Listar Propostas", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"AccountId\": \"{{account_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/proposals", "host": ["{{base_url}}"], "path": ["api", "proposals"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ProposalId\": \"PROP123456\",\n  \"plantaUrl\": \"https://example.com/nova-planta.pdf\"\n}"}, "url": {"raw": "{{base_url}}/api/proposal/updateplanta", "host": ["{{base_url}}"], "path": ["api", "proposal", "updateplanta"]}}}]}, {"name": "Empreendimentos", "item": [{"name": "<PERSON><PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/empreendimento/all", "host": ["{{base_url}}"], "path": ["api", "empreendimento", "all"]}}}, {"name": "Filtrar para Vizinhos", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/empreendimento/filterToVizinho", "host": ["{{base_url}}"], "path": ["api", "empreendimento", "filterToVizinho"]}}}, {"name": "Empreendimento Específico", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/empreendimento/EMP123456", "host": ["{{base_url}}"], "path": ["api", "empreendimento", "EMP123456"]}}}, {"name": "Empreendimento POST", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"EmpreendimentoId\": \"EMP123456\"\n}"}, "url": {"raw": "{{base_url}}/api/Empreendimento", "host": ["{{base_url}}"], "path": ["api", "Empreendimento"]}}}]}, {"name": "Agendamentos", "item": [{"name": "Listar Agendamentos", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/schedule", "host": ["{{base_url}}"], "path": ["api", "schedule"]}}}, {"name": "Opções de Serviços", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/scheduleServiceOptions", "host": ["{{base_url}}"], "path": ["api", "scheduleServiceOptions"]}}}]}, {"name": "Conhecimento", "item": [{"name": "<PERSON>r <PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/knowledge/showByEtapa/PROP123456", "host": ["{{base_url}}"], "path": ["api", "knowledge", "showByEtapa", "PROP123456"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/knowledge/faq", "host": ["{{base_url}}"], "path": ["api", "knowledge", "faq"]}}}, {"name": "<PERSON>em Específico", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/knowledge/show/KNOW123456", "host": ["{{base_url}}"], "path": ["api", "knowledge", "show", "KNOW123456"]}}}]}, {"name": "Outros", "item": [{"name": "Conte<PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/contents", "host": ["{{base_url}}"], "path": ["api", "contents"]}}}, {"name": "Categorias de Vídeos", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/showCategorias", "host": ["{{base_url}}"], "path": ["api", "showCategorias"]}}}, {"name": "Vídeos por Categoria", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/showVideosByCategoria?categoriaId=CAT123", "host": ["{{base_url}}"], "path": ["api", "showVideosByCategoria"], "query": [{"key": "categoriaId", "value": "CAT123"}]}}}, {"name": "Indicar Amigo", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nomeAmigo\": \"<PERSON>\",\n  \"emailAmigo\": \"<EMAIL>\",\n  \"telefoneAmigo\": \"(11) 99999-9999\",\n  \"mensagem\": \"Recomendo a Cury!\"\n}"}, "url": {"raw": "{{base_url}}/api/sendReferAFriend", "host": ["{{base_url}}"], "path": ["api", "sendReferAFriend"]}}}, {"name": "<PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/clara?pergunta=Como está minha obra?", "host": ["{{base_url}}"], "path": ["api", "clara"], "query": [{"key": "per<PERSON><PERSON>", "value": "Como está minha obra?"}]}}}, {"name": "Regulamento Cury Cheg<PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/chegamais/regulamento", "host": ["{{base_url}}"], "path": ["api", "chegama<PERSON>", "regulamento"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"AccountId\": \"{{account_id}}\",\n  \"aceiteTermos\": true\n}"}, "url": {"raw": "{{base_url}}/api/chegamais/join", "host": ["{{base_url}}"], "path": ["api", "chegama<PERSON>", "join"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"AccountId\": \"{{account_id}}\",\n  \"ProposalId\": \"PROP123456\"\n}"}, "url": {"raw": "{{base_url}}/api/contract/show", "host": ["{{base_url}}"], "path": ["api", "contract", "show"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script executado antes de cada requisição", "console.log('Executando requisição para:', pm.request.url);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script executado após cada requisição", "pm.test('Status code é válido', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204, 400, 401, 404, 422, 500]);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    pm.test('Response tem formato JSON válido', function () {", "        pm.response.to.have.jsonBody();", "    });", "}"]}}]}